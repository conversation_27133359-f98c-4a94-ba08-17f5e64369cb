# frozen_string_literal: true

require "spec_helper"
require "sentry/rails/log_subscribers/action_mailer_subscriber"

RSpec.describe Sentry::Rails::LogSubscribers::ActionMailerSubscriber do
  before do
    make_basic_app do |config|
      config.enable_logs = true
      config.rails.structured_logging.enabled = true
      config.rails.structured_logging.attach_to = [:action_mailer]
    end
  end

  describe "integration with ActiveSupport::Notifications" do
    it "logs deliver events when emails are sent" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "UserMailer",
        perform_deliveries: true,
        delivery_method: :test,
        date: Time.current,
        message_id: "<EMAIL>"
      ) do
        sleep(0.01)
      end

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
      expect(log_event).not_to be_nil
      expect(log_event[:level]).to eq("info")
      expect(log_event[:attributes][:mailer][:value]).to eq("UserMailer")
      expect(log_event[:attributes][:duration_ms][:value]).to be > 0
      expect(log_event[:attributes][:perform_deliveries][:value]).to be true
      expect(log_event[:attributes][:delivery_method][:value]).to eq(:test)
      expect(log_event[:attributes][:date]).to be_present
    end

    it "logs process events when mailer actions are processed" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("process.action_mailer",
        mailer: "UserMailer",
        action: "welcome_email",
        params: { user_id: 123, name: "John Doe" }
      ) do
        sleep(0.01)
      end

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
      expect(log_event).not_to be_nil
      expect(log_event[:level]).to eq("info")
      expect(log_event[:attributes][:mailer][:value]).to eq("UserMailer")
      expect(log_event[:attributes][:action][:value]).to eq("welcome_email")
      expect(log_event[:attributes][:duration_ms][:value]).to be > 0
    end

    it "includes delivery method when available" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "NotificationMailer",
        perform_deliveries: true,
        delivery_method: :smtp
      )

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "Email delivered via NotificationMailer" }
      expect(log_event).not_to be_nil
      expect(log_event[:attributes][:delivery_method][:value]).to eq(:smtp)
    end

    context "when send_default_pii is enabled" do
      before do
        Sentry.configuration.send_default_pii = true
      end

      after do
        Sentry.configuration.send_default_pii = false
      end

      it "includes message_id for deliver events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("deliver.action_mailer",
          mailer: "UserMailer",
          perform_deliveries: true,
          message_id: "<EMAIL>"
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes][:message_id][:value]).to eq("<EMAIL>")
      end

      it "includes filtered parameters for process events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("process.action_mailer",
          mailer: "UserMailer",
          action: "welcome_email",
          params: {
            user_id: 123,
            safe_param: "value",
            password: "secret",
            email_address: "<EMAIL>",
            subject: "Welcome!",
            api_key: "secret-key"
          }
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes][:params]).to be_present

        params = log_event[:attributes][:params][:value]
        expect(params).to include(user_id: 123, safe_param: "value")
        expect(params).not_to have_key(:password)
        expect(params).not_to have_key(:email_address)
        expect(params).not_to have_key(:subject)
        expect(params).not_to have_key(:api_key)
      end
    end

    context "when send_default_pii is disabled" do
      it "does not include message_id for deliver events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("deliver.action_mailer",
          mailer: "UserMailer",
          perform_deliveries: true,
          message_id: "<EMAIL>"
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes]).not_to have_key(:message_id)
      end

      it "does not include parameters for process events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("process.action_mailer",
          mailer: "UserMailer",
          action: "welcome_email",
          params: { user_id: 123, name: "John Doe" }
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes]).not_to have_key(:params)
      end
    end

    it "excludes events starting with !" do
      subscriber = described_class.new
      event = double("event", name: "!connection.action_mailer", payload: {})
      expect(subscriber.send(:excluded_event?, event)).to be true
    end
  end

  describe "when logging is disabled" do
    before do
      make_basic_app do |config|
        config.enable_logs = false
        config.rails.structured_logging.enabled = true
        config.rails.structured_logging.attach_to = [:action_mailer]
      end
    end

    it "does not log events when logging is disabled" do
      initial_log_count = sentry_logs.count

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "UserMailer",
        perform_deliveries: true
      )

      if Sentry.get_current_client&.log_event_buffer
        Sentry.get_current_client.log_event_buffer.flush
      end

      expect(sentry_logs.count).to eq(initial_log_count)
    end
  end
end
